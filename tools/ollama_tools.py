"""
Ollama-specific tools for local LLM management and interaction.
"""

from typing import List, Dict, Any, Optional
import mcp
from mcp.shared.exceptions import McpError
from mcp.types import ErrorData, INVALID_REQUEST
from utils.client_utils import ClientUtils
from utils.ollama_client import OllamaClient, OllamaModel


# Get MCP instance
mcp = ClientUtils.get_mcp_instance()


@mcp.tool()
def check_ollama_status() -> Dict[str, Any]:
    """
    Check if Ollama is running and accessible.
    
    Returns:
        Dict[str, Any]: Status information including availability and base URL
    """
    try:
        ollama_client = ClientUtils.get_ollama_client()
        is_available = ollama_client.is_available()
        
        return {
            "available": is_available,
            "base_url": ollama_client.base_url,
            "status": "running" if is_available else "not_running",
            "message": "Ollama is accessible" if is_available else "Ollama is not running or not accessible"
        }
    except Exception as e:
        return {
            "available": False,
            "status": "error",
            "message": f"Error checking Ollama status: {str(e)}"
        }


@mcp.tool()
def list_ollama_models() -> List[Dict[str, Any]]:
    """
    List all available models in the local Ollama instance.
    
    Returns:
        List[Dict[str, Any]]: List of available models with their metadata
        
    Raises:
        McpError: If Ollama is not available or if listing models fails
    """
    try:
        ollama_client = ClientUtils.get_ollama_client()
        
        if not ollama_client.is_available():
            raise McpError(
                ErrorData(
                    code=INVALID_REQUEST,
                    message="Ollama is not running or not accessible. Please start Ollama first."
                )
            )
        
        models = ollama_client.list_models()
        
        return [
            {
                "name": model.name,
                "size": model.size,
                "size_gb": round(model.size / (1024**3), 2),
                "modified_at": model.modified_at,
                "digest": model.digest[:12] + "...",  # Truncate for readability
                "details": model.details
            }
            for model in models
        ]
    except McpError:
        raise
    except Exception as e:
        raise McpError(
            ErrorData(
                code=INVALID_REQUEST,
                message=f"Failed to list Ollama models: {str(e)}"
            )
        )


@mcp.tool()
def pull_ollama_model(model_name: str) -> Dict[str, Any]:
    """
    Pull a model from the Ollama registry.
    
    Args:
        model_name: Name of the model to pull (e.g., 'llama2', 'mistral', 'codellama')
        
    Returns:
        Dict[str, Any]: Pull operation result
        
    Raises:
        McpError: If Ollama is not available or if pull operation fails
    """
    try:
        ollama_client = ClientUtils.get_ollama_client()
        
        if not ollama_client.is_available():
            raise McpError(
                ErrorData(
                    code=INVALID_REQUEST,
                    message="Ollama is not running or not accessible. Please start Ollama first."
                )
            )
        
        result = ollama_client.pull_model(model_name)
        return result
    except McpError:
        raise
    except Exception as e:
        raise McpError(
            ErrorData(
                code=INVALID_REQUEST,
                message=f"Failed to pull model '{model_name}': {str(e)}"
            )
        )


@mcp.tool()
def remove_ollama_model(model_name: str) -> Dict[str, Any]:
    """
    Remove a model from the local Ollama instance.
    
    Args:
        model_name: Name of the model to remove
        
    Returns:
        Dict[str, Any]: Remove operation result
        
    Raises:
        McpError: If Ollama is not available or if remove operation fails
    """
    try:
        ollama_client = ClientUtils.get_ollama_client()
        
        if not ollama_client.is_available():
            raise McpError(
                ErrorData(
                    code=INVALID_REQUEST,
                    message="Ollama is not running or not accessible. Please start Ollama first."
                )
            )
        
        result = ollama_client.remove_model(model_name)
        return result
    except McpError:
        raise
    except Exception as e:
        raise McpError(
            ErrorData(
                code=INVALID_REQUEST,
                message=f"Failed to remove model '{model_name}': {str(e)}"
            )
        )


@mcp.tool()
def get_ollama_model_info(model_name: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific Ollama model.
    
    Args:
        model_name: Name of the model to get information about
        
    Returns:
        Dict[str, Any]: Detailed model information
        
    Raises:
        McpError: If Ollama is not available or if getting model info fails
    """
    try:
        ollama_client = ClientUtils.get_ollama_client()
        
        if not ollama_client.is_available():
            raise McpError(
                ErrorData(
                    code=INVALID_REQUEST,
                    message="Ollama is not running or not accessible. Please start Ollama first."
                )
            )
        
        model_info = ollama_client.get_model_info(model_name)
        return model_info
    except McpError:
        raise
    except Exception as e:
        raise McpError(
            ErrorData(
                code=INVALID_REQUEST,
                message=f"Failed to get model info for '{model_name}': {str(e)}"
            )
        )


@mcp.tool()
def chat_with_ollama_model(
    model_name: str,
    message: str,
    system_prompt: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: Optional[int] = None
) -> Dict[str, Any]:
    """
    Chat with a local Ollama model.
    
    Args:
        model_name: Name of the model to chat with
        message: User message to send to the model
        system_prompt: Optional system prompt to set context
        temperature: Sampling temperature (0.0 to 1.0, default: 0.7)
        max_tokens: Maximum tokens to generate (optional)
        
    Returns:
        Dict[str, Any]: Chat completion response
        
    Raises:
        McpError: If Ollama is not available or if chat completion fails
    """
    try:
        ollama_client = ClientUtils.get_ollama_client()
        
        if not ollama_client.is_available():
            raise McpError(
                ErrorData(
                    code=INVALID_REQUEST,
                    message="Ollama is not running or not accessible. Please start Ollama first."
                )
            )
        
        # Build messages list
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": message})
        
        response = ollama_client.chat_completion(
            model=model_name,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return response
    except McpError:
        raise
    except Exception as e:
        raise McpError(
            ErrorData(
                code=INVALID_REQUEST,
                message=f"Failed to chat with model '{model_name}': {str(e)}"
            )
        )


@mcp.tool()
def get_recommended_ollama_models() -> List[Dict[str, Any]]:
    """
    Get a list of recommended Ollama models for different use cases.
    
    Returns:
        List[Dict[str, Any]]: List of recommended models with descriptions
    """
    return [
        {
            "name": "llama3.2",
            "size": "2.0GB",
            "description": "Latest Llama model, good for general conversation and reasoning",
            "use_cases": ["general chat", "reasoning", "code assistance"],
            "pull_command": "ollama pull llama3.2"
        },
        {
            "name": "mistral",
            "size": "4.1GB", 
            "description": "Fast and efficient model for various tasks",
            "use_cases": ["general chat", "text generation", "summarization"],
            "pull_command": "ollama pull mistral"
        },
        {
            "name": "codellama",
            "size": "3.8GB",
            "description": "Specialized for code generation and programming tasks",
            "use_cases": ["code generation", "code explanation", "debugging"],
            "pull_command": "ollama pull codellama"
        },
        {
            "name": "phi3",
            "size": "2.3GB",
            "description": "Small but capable model from Microsoft",
            "use_cases": ["general chat", "reasoning", "lightweight tasks"],
            "pull_command": "ollama pull phi3"
        },
        {
            "name": "gemma2",
            "size": "1.6GB",
            "description": "Google's efficient model for various tasks",
            "use_cases": ["general chat", "text processing", "lightweight tasks"],
            "pull_command": "ollama pull gemma2"
        }
    ]
