#!/usr/bin/env python3
"""
Example script demonstrating Ollama integration with the MCP server.
This script shows how to use the Ollama client directly.
"""

import os
import sys
import asyncio

# Add the parent directory to the path so we can import from utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.ollama_client import OllamaClient


async def main():
    """Demonstrate Ollama functionality."""
    print("🦙 Ollama Integration Example")
    print("=" * 40)
    
    # Initialize Ollama client
    ollama_url = os.getenv("OLLAMA_URL", "http://localhost:11434")
    client = OllamaClient(ollama_url)
    
    # Check if Ollama is available
    print(f"Checking Ollama at {ollama_url}...")
    if not client.is_available():
        print("❌ Ollama is not running or not accessible.")
        print("Please start Ollama first:")
        print("  - Run 'ollama serve' in a terminal")
        print("  - Or start the Ollama app")
        return
    
    print("✅ Ollama is running!")
    print()
    
    # List available models
    print("📋 Available Models:")
    try:
        models = client.list_models()
        if not models:
            print("No models found. Pull a model first:")
            print("  ollama pull llama3.2")
            return
        
        for model in models:
            size_gb = round(model.size / (1024**3), 2)
            print(f"  • {model.name} ({size_gb} GB)")
        print()
        
        # Use the first available model for chat
        model_name = models[0].name
        print(f"💬 Chatting with {model_name}:")
        
        # Simple chat example
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is the capital of France?"}
        ]
        
        print("User: What is the capital of France?")
        response = client.chat_completion(
            model=model_name,
            messages=messages,
            temperature=0.7
        )
        
        assistant_message = response['choices'][0]['message']['content']
        print(f"Assistant: {assistant_message}")
        print()
        
        # Show model info
        print(f"ℹ️  Model Information for {model_name}:")
        try:
            model_info = client.get_model_info(model_name)
            print(f"  • Template: {model_info.get('template', 'N/A')[:100]}...")
            print(f"  • Parameters: {model_info.get('details', {}).get('parameter_size', 'N/A')}")
            print(f"  • Quantization: {model_info.get('details', {}).get('quantization_level', 'N/A')}")
        except Exception as e:
            print(f"  Could not get model info: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
