#!/usr/bin/env python3
"""
Test script to verify the MCP server works correctly with MCPHost.
This script demonstrates the integration without requiring actual Control Plane credentials.
"""

import os
import sys
import json
import tempfile
import subprocess
from pathlib import Path


def create_test_config():
    """Create a test MCPHost configuration file."""
    config = {
        "mcpServers": {
            "control-plane": {
                "command": "uv",
                "args": [
                    "run",
                    "--directory",
                    str(Path.cwd()),
                    "main.py"
                ]
            }
        }
    }
    
    # Create temporary config file
    config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    json.dump(config, config_file, indent=2)
    config_file.close()
    
    return config_file.name


def test_mcp_server_startup():
    """Test that the MCP server can start up correctly."""
    print("🧪 Testing MCP Server Startup")
    print("=" * 40)
    
    # Set minimal environment variables for testing
    test_env = os.environ.copy()
    test_env.update({
        "CONTROL_PLANE_URL": "https://test.example.com",
        "FACETS_USERNAME": "test_user",
        "FACETS_TOKEN": "test_token"
    })
    
    try:
        # Try to start the server and see if it initializes without crashing
        result = subprocess.run(
            ["uv", "run", "main.py"],
            env=test_env,
            capture_output=True,
            text=True,
            timeout=10,
            input=""  # Send empty input to stdin
        )
        
        # The server should fail login but not crash during initialization
        if "Login test failed" in result.stdout or "Login test failed" in result.stderr:
            print("✅ MCP server initialized correctly (login failed as expected with test credentials)")
            return True
        else:
            print("❌ Unexpected server behavior")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("✅ MCP server started successfully (timeout expected for interactive mode)")
        return True
    except Exception as e:
        print(f"❌ Error starting MCP server: {e}")
        return False


def test_mcphost_config():
    """Test MCPHost configuration generation."""
    print("\n🔧 Testing MCPHost Configuration")
    print("=" * 40)
    
    try:
        config_file = create_test_config()
        
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Verify config structure
        assert "mcpServers" in config
        assert "control-plane" in config["mcpServers"]
        assert "command" in config["mcpServers"]["control-plane"]
        assert "args" in config["mcpServers"]["control-plane"]
        
        print("✅ MCPHost configuration generated successfully")
        print(f"📁 Config file: {config_file}")
        print("📋 Configuration:")
        print(json.dumps(config, indent=2))
        
        # Clean up
        os.unlink(config_file)
        return True
        
    except Exception as e:
        print(f"❌ Error creating MCPHost config: {e}")
        return False


def show_usage_instructions():
    """Show instructions for using the MCP server with MCPHost."""
    print("\n📖 Usage Instructions")
    print("=" * 40)
    
    print("To use this MCP server with MCPHost and local LLMs:")
    print()
    print("1. Install MCPHost:")
    print("   go install github.com/mark3labs/mcphost@latest")
    print()
    print("2. Create ~/.mcp.json configuration:")
    config = {
        "mcpServers": {
            "control-plane": {
                "command": "uv",
                "args": [
                    "run",
                    "--directory",
                    str(Path.cwd()),
                    "main.py"
                ]
            }
        }
    }
    print(json.dumps(config, indent=2))
    print()
    print("3. Set environment variables:")
    print("   export CONTROL_PLANE_URL='https://your-control-plane-url'")
    print("   export FACETS_TOKEN='your-facets-token'")
    print("   export FACETS_USERNAME='your-facets-username'")
    print()
    print("4. Run MCPHost with your preferred model:")
    print("   # With Ollama")
    print("   mcphost -m ollama:llama3.2")
    print()
    print("   # With OpenAI-compatible models")
    print("   mcphost -m openai:gpt-4")
    print()
    print("5. Start chatting with your LLM and use Control Plane tools!")
    print("   Example: 'List all my projects' or 'Show me environments in my project'")


def main():
    """Run all tests."""
    print("🚀 Control Plane MCP Server - MCPHost Integration Test")
    print("=" * 60)
    
    # Check if uv is available
    try:
        subprocess.run(["uv", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ uv is not installed or not in PATH")
        print("Please install uv: brew install uv")
        return False
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_mcp_server_startup():
        tests_passed += 1
    
    if test_mcphost_config():
        tests_passed += 1
    
    # Show results
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The MCP server is ready for use with MCPHost.")
        show_usage_instructions()
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
