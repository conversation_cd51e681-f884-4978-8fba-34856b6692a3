# Initialize configuration and MCP instance
import swagger_client
from utils.client_utils import Client<PERSON>tils
from tools import *


def _test_login() -> bool:
    """
    Test login using the ApplicationController.
    Only tests Control Plane login if Control Plane is enabled.

    Returns:
        bool: True if login is successful or not required, False otherwise.
    """
    # Skip login test for ollama-only mode
    if not ClientUtils.is_control_plane_enabled():
        print("Running in Ollama-only mode, skipping Control Plane login test")
        return True

    try:
        api_instance = swagger_client.ApplicationControllerApi(ClientUtils.get_client())
        api_instance.me_using_get()
        print("Control Plane login successful")
        return True
    except Exception as e:
        print(f"Control Plane login test failed: {e}")
        return False


def _test_ollama() -> bool:
    """
    Test Ollama connection if Ollama is enabled.

    Returns:
        bool: True if Ollama is accessible or not required, False otherwise.
    """
    if not ClientUtils.is_ollama_enabled():
        return True

    try:
        ollama_client = ClientUtils.get_ollama_client()
        if ollama_client.is_available():
            print("Ollama connection successful")
            return True
        else:
            print("Warning: Ollama is not running or not accessible")
            return True  # Don't fail startup, just warn
    except Exception as e:
        print(f"Ollama connection test failed: {e}")
        return True  # Don't fail startup, just warn


if __name__ == "__main__":
    print(f"Starting MCP server in {ClientUtils.get_mode()} mode...")

    mcp = ClientUtils.get_mcp_instance()

    # Test connections based on mode
    login_ok = _test_login()
    ollama_ok = _test_ollama()

    if login_ok:
        print("MCP server starting...")
        mcp.run(transport="stdio")
    else:
        print("Failed to initialize. Check your configuration.")
        exit(1)
