"""
Ollama client utilities for local LLM integration.
Provides OpenAI-compatible interface to Ollama models.
"""

import os
import requests
import json
from typing import List, Dict, Any, Optional
from openai import OpenAI
from dataclasses import dataclass


@dataclass
class OllamaModel:
    """Represents an Ollama model with metadata."""
    name: str
    size: int
    modified_at: str
    digest: str
    details: Dict[str, Any]


class OllamaClient:
    """Client for interacting with local Ollama instance."""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        """
        Initialize Ollama client.
        
        Args:
            base_url: Base URL for Ollama API (default: http://localhost:11434)
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api"
        self.openai_url = f"{self.base_url}/v1"
        
        # Initialize OpenAI-compatible client
        self.openai_client = OpenAI(
            base_url=self.openai_url,
            api_key="ollama"  # Required but unused by Ollama
        )
    
    def is_available(self) -> bool:
        """
        Check if Ollama is running and accessible.
        
        Returns:
            bool: True if Ollama is available, False otherwise
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def list_models(self) -> List[OllamaModel]:
        """
        List all available models in Ollama.
        
        Returns:
            List[OllamaModel]: List of available models
            
        Raises:
            Exception: If Ollama is not available or request fails
        """
        try:
            response = requests.get(f"{self.api_url}/tags")
            response.raise_for_status()
            
            data = response.json()
            models = []
            
            for model_data in data.get('models', []):
                model = OllamaModel(
                    name=model_data['name'],
                    size=model_data['size'],
                    modified_at=model_data['modified_at'],
                    digest=model_data['digest'],
                    details=model_data.get('details', {})
                )
                models.append(model)
            
            return models
        except Exception as e:
            raise Exception(f"Failed to list Ollama models: {str(e)}")
    
    def pull_model(self, model_name: str) -> Dict[str, Any]:
        """
        Pull a model from Ollama registry.
        
        Args:
            model_name: Name of the model to pull
            
        Returns:
            Dict[str, Any]: Pull operation result
            
        Raises:
            Exception: If pull operation fails
        """
        try:
            response = requests.post(
                f"{self.api_url}/pull",
                json={"name": model_name},
                stream=True
            )
            response.raise_for_status()
            
            # Return the final status
            result = {"status": "success", "model": model_name}
            for line in response.iter_lines():
                if line:
                    data = json.loads(line)
                    if data.get('status') == 'success':
                        result.update(data)
            
            return result
        except Exception as e:
            raise Exception(f"Failed to pull model '{model_name}': {str(e)}")
    
    def remove_model(self, model_name: str) -> Dict[str, Any]:
        """
        Remove a model from Ollama.
        
        Args:
            model_name: Name of the model to remove
            
        Returns:
            Dict[str, Any]: Remove operation result
            
        Raises:
            Exception: If remove operation fails
        """
        try:
            response = requests.delete(
                f"{self.api_url}/delete",
                json={"name": model_name}
            )
            response.raise_for_status()
            
            return {"status": "success", "message": f"Model '{model_name}' removed successfully"}
        except Exception as e:
            raise Exception(f"Failed to remove model '{model_name}': {str(e)}")
    
    def chat_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        Create a chat completion using OpenAI-compatible interface.
        
        Args:
            model: Model name to use
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum tokens to generate
            stream: Whether to stream the response
            
        Returns:
            Dict[str, Any]: Chat completion response
            
        Raises:
            Exception: If chat completion fails
        """
        try:
            kwargs = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "stream": stream
            }
            
            if max_tokens is not None:
                kwargs["max_tokens"] = max_tokens
            
            response = self.openai_client.chat.completions.create(**kwargs)
            
            if stream:
                return response
            else:
                return {
                    "id": response.id,
                    "object": response.object,
                    "created": response.created,
                    "model": response.model,
                    "choices": [
                        {
                            "index": choice.index,
                            "message": {
                                "role": choice.message.role,
                                "content": choice.message.content
                            },
                            "finish_reason": choice.finish_reason
                        }
                        for choice in response.choices
                    ],
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                        "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                        "total_tokens": response.usage.total_tokens if response.usage else 0
                    }
                }
        except Exception as e:
            raise Exception(f"Chat completion failed: {str(e)}")
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dict[str, Any]: Model information
            
        Raises:
            Exception: If model info retrieval fails
        """
        try:
            response = requests.post(
                f"{self.api_url}/show",
                json={"name": model_name}
            )
            response.raise_for_status()
            
            return response.json()
        except Exception as e:
            raise Exception(f"Failed to get model info for '{model_name}': {str(e)}")
